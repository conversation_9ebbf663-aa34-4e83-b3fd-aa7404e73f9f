<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Edit, Delete, User, ArrowDown, ArrowUp } from '@element-plus/icons-vue';
import { useRolesStore } from '@/stores/rolesStore';
import type { Role, RoleTreeNode } from '@/types';

const props = defineProps<{
    tree: RoleTreeNode[];
    canManage: boolean;
}>();

const emit = defineEmits<{
    'edit-role': [role: Role];
    'delete-role': [role: Role];
    'manage-members': [role: Role];
}>();

const rolesStore = useRolesStore();

// Drag and drop state
const draggedNode = ref<RoleTreeNode | null>(null);
const dropTarget = ref<RoleTreeNode | null>(null);
const isDragging = ref(false);

// Tree expansion state
const expandedNodes = ref<Set<string>>(new Set());

// Computed properties
const hasRootNodes = computed(() => props.tree.length > 0);

// Tree node methods
const toggleExpanded = (nodeId: string) => {
    if (expandedNodes.value.has(nodeId)) {
        expandedNodes.value.delete(nodeId);
    } else {
        expandedNodes.value.add(nodeId);
    }
};

const isExpanded = (nodeId: string) => {
    return expandedNodes.value.has(nodeId);
};

// Drag and drop handlers
const handleDragStart = (event: DragEvent, node: RoleTreeNode) => {
    if (!props.canManage) {
        event.preventDefault();
        return;
    }
    
    draggedNode.value = node;
    isDragging.value = true;
    
    if (event.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', node.id);
    }
};

const handleDragOver = (event: DragEvent, node: RoleTreeNode) => {
    if (!props.canManage || !draggedNode.value) return;
    
    // Prevent dropping on self or descendants
    if (draggedNode.value.id === node.id || isDescendant(draggedNode.value, node)) {
        return;
    }
    
    event.preventDefault();
    dropTarget.value = node;
};

const handleDragLeave = () => {
    dropTarget.value = null;
};

const handleDrop = async (event: DragEvent, targetNode: RoleTreeNode) => {
    event.preventDefault();
    
    if (!props.canManage || !draggedNode.value || draggedNode.value.id === targetNode.id) {
        return;
    }
    
    // Prevent creating cycles
    if (isDescendant(draggedNode.value, targetNode)) {
        ElMessage.warning('Cannot move a role to its own descendant');
        return;
    }
    
    try {
        const success = await rolesStore.setRoleParent(draggedNode.value.id, targetNode.id);
        if (success) {
            ElMessage.success('Role hierarchy updated successfully');
        }
    } catch (error) {
        console.error('Failed to update role hierarchy:', error);
        ElMessage.error('Failed to update role hierarchy');
    } finally {
        draggedNode.value = null;
        dropTarget.value = null;
        isDragging.value = false;
    }
};

const handleDragEnd = () => {
    draggedNode.value = null;
    dropTarget.value = null;
    isDragging.value = false;
};

// Utility functions
const isDescendant = (ancestor: RoleTreeNode, node: RoleTreeNode): boolean => {
    const checkChildren = (children: RoleTreeNode[]): boolean => {
        for (const child of children) {
            if (child.id === ancestor.id) return true;
            if (checkChildren(child.children)) return true;
        }
        return false;
    };
    
    return checkChildren(node.children);
};

const getRoleColor = (color?: string) => {
    return color || '#6b7280';
};

// Event handlers
const handleEditRole = (node: RoleTreeNode) => {
    const role = rolesStore.getRole(node.id);
    if (role) {
        emit('edit-role', role);
    }
};

const handleDeleteRole = (node: RoleTreeNode) => {
    const role = rolesStore.getRole(node.id);
    if (role) {
        emit('delete-role', role);
    }
};

const handleManageMembers = (node: RoleTreeNode) => {
    const role = rolesStore.getRole(node.id);
    if (role) {
        emit('manage-members', role);
    }
};
</script>

<template>
    <div class="role-hierarchy-tree">
        <div v-if="!hasRootNodes" class="empty-state">
            <p>No roles found. Create your first role to get started.</p>
        </div>
        
        <div v-else class="tree-container">
            <div class="tree-instructions" v-if="canManage">
                <el-alert
                    title="Drag and drop roles to reorganize the hierarchy"
                    type="info"
                    :closable="false"
                    show-icon
                />
            </div>
            
            <div class="tree-nodes">
                <RoleTreeNode
                    v-for="node in tree"
                    :key="node.id"
                    :node="node"
                    :level="0"
                    :can-manage="canManage"
                    :is-expanded="isExpanded(node.id)"
                    :is-drag-target="dropTarget?.id === node.id"
                    :is-dragging="isDragging"
                    @toggle-expanded="toggleExpanded"
                    @drag-start="handleDragStart"
                    @drag-over="handleDragOver"
                    @drag-leave="handleDragLeave"
                    @drop="handleDrop"
                    @drag-end="handleDragEnd"
                    @edit-role="handleEditRole"
                    @delete-role="handleDeleteRole"
                    @manage-members="handleManageMembers"
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
// Recursive tree node component
const RoleTreeNode = {
    name: 'RoleTreeNode',
    props: {
        node: Object as () => RoleTreeNode,
        level: Number,
        canManage: Boolean,
        isExpanded: Boolean,
        isDragTarget: Boolean,
        isDragging: Boolean
    },
    emits: [
        'toggle-expanded',
        'drag-start',
        'drag-over', 
        'drag-leave',
        'drop',
        'drag-end',
        'edit-role',
        'delete-role',
        'manage-members'
    ],
    template: `
        <div class="tree-node" :style="{ marginLeft: level * 24 + 'px' }">
            <div 
                class="node-content"
                :class="{ 
                    'drag-target': isDragTarget,
                    'dragging': isDragging 
                }"
                :draggable="canManage"
                @dragstart="$emit('drag-start', $event, node)"
                @dragover="$emit('drag-over', $event, node)"
                @dragleave="$emit('drag-leave')"
                @drop="$emit('drop', $event, node)"
                @dragend="$emit('drag-end')"
            >
                <div class="node-info">
                    <el-button
                        v-if="node.children.length > 0"
                        :icon="isExpanded ? ArrowDown : ArrowUp"
                        size="small"
                        text
                        @click="$emit('toggle-expanded', node.id)"
                        class="expand-button"
                    />
                    <div class="role-indicator">
                        <div 
                            class="role-color"
                            :style="{ backgroundColor: getRoleColor(node.color) }"
                        ></div>
                    </div>
                    <div class="role-details">
                        <div class="role-name">{{ node.name }}</div>
                        <div class="role-meta">
                            <span class="member-count">{{ node.member_count }} members</span>
                            <span v-if="node.description" class="role-description">
                                {{ node.description }}
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="node-actions" v-if="canManage">
                    <el-button-group size="small">
                        <el-button :icon="User" @click="$emit('manage-members', node)" title="Manage Members" />
                        <el-button :icon="Edit" @click="$emit('edit-role', node)" title="Edit Role" />
                        <el-button :icon="Delete" type="danger" @click="$emit('delete-role', node)" title="Delete Role" />
                    </el-button-group>
                </div>
            </div>
            
            <div v-if="isExpanded && node.children.length > 0" class="node-children">
                <RoleTreeNode
                    v-for="child in node.children"
                    :key="child.id"
                    :node="child"
                    :level="level + 1"
                    :can-manage="canManage"
                    :is-expanded="$parent.isExpanded(child.id)"
                    :is-drag-target="$parent.dropTarget?.id === child.id"
                    :is-dragging="isDragging"
                    @toggle-expanded="$emit('toggle-expanded', $event)"
                    @drag-start="$emit('drag-start', $event, $event)"
                    @drag-over="$emit('drag-over', $event, $event)"
                    @drag-leave="$emit('drag-leave')"
                    @drop="$emit('drop', $event, $event)"
                    @drag-end="$emit('drag-end')"
                    @edit-role="$emit('edit-role', $event)"
                    @delete-role="$emit('delete-role', $event)"
                    @manage-members="$emit('manage-members', $event)"
                />
            </div>
        </div>
    `
};
</script>

<style scoped>
.role-hierarchy-tree {
    padding: 16px;
}

.empty-state {
    text-align: center;
    padding: 48px 16px;
    color: var(--el-text-color-regular);
}

.tree-instructions {
    margin-bottom: 16px;
}

.tree-nodes {
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    background: var(--el-bg-color);
}

.tree-node {
    border-bottom: 1px solid var(--el-border-color-lighter);
}

.tree-node:last-child {
    border-bottom: none;
}

.node-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    transition: all 0.2s ease;
    cursor: grab;
}

.node-content:hover {
    background-color: var(--el-fill-color-light);
}

.node-content.drag-target {
    background-color: var(--el-color-primary-light-9);
    border: 2px dashed var(--el-color-primary);
}

.node-content.dragging {
    opacity: 0.5;
    cursor: grabbing;
}

.node-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.expand-button {
    width: 24px;
    height: 24px;
    padding: 0;
}

.role-indicator {
    display: flex;
    align-items: center;
}

.role-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.role-details {
    flex: 1;
}

.role-name {
    font-weight: 500;
    color: var(--el-text-color-primary);
    margin-bottom: 2px;
}

.role-meta {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: var(--el-text-color-regular);
}

.member-count {
    font-weight: 500;
}

.role-description {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.node-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.node-content:hover .node-actions {
    opacity: 1;
}

.node-children {
    background-color: var(--el-fill-color-extra-light);
}
</style>
