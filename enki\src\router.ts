import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from './pages/Dashboard.vue'
import Kanban from './pages/Kanban/Kanban.vue'
import Settings from './pages/Settings.vue'
import Profile from './pages/Profile.vue'
import Contacts from './pages/Contacts.vue'
import UserProfile from './components/UserProfile.vue'
import AgentProfile from './components/AgentProfile.vue'
import AdminRoles from './pages/Admin/AdminRoles.vue'

const routes = [
	{
		path: '/:spaceId',
		name: 'Dashboard',
		component: Dashboard,
		meta: { requiresAuth: true },
		props: true
	},
	{
		path: '/:spaceId/kanban/:projectId',
		name: 'Kanban',
		component: Kanban,
		meta: { requiresAuth: true },
		props: true
	},
	{
		path: '/profile',
		name: 'Profile',
		component: Profile,
		meta: { requiresAuth: true },
	},
	{
		path: '/settings',
		name: 'Setting<PERSON>',
		component: Settings,
	},
	{
		path: '/contacts',
		name: 'Contacts',
		component: Contacts,
		meta: { requiresAuth: true },
	},
	{
		path: '/profile/user/:userId',
		name: 'UserProfile',
		component: UserProfile,
		meta: { requiresAuth: true },
		props: true
	},
	{
		path: '/profile/agent/:agentId',
		name: 'AgentProfile',
		component: AgentProfile,
		meta: { requiresAuth: true },
		props: true
	},
	{
		path: '/admin/roles',
		name: 'AdminRoles',
		component: AdminRoles,
		meta: { requiresAuth: true }
	}
];

const router = createRouter({
	history: createWebHistory("enki"),
	routes,
});

function init(user: any) {
	router.beforeEach((to, _from, next) => {
		if (
			!to.matched.some(record => record.meta.requiresAuth)
			|| user.value
		) return next();
	});
}

export { init, router };