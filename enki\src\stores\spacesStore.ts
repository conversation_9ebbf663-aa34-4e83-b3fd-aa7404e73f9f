import { defineStore } from 'pinia';
import { collection, doc, Firestore, getFirestore, getDoc, getDocs, CollectionReference, type DocumentData, DocumentReference } from 'firebase/firestore';
import { connectToKiServer } from '@/services/kiApi';
import { useKanbanStore } from './kanbanStore';
import type { SpaceDefinition } from '@/types';
import { inject, type Ref } from 'vue';
import type { User } from 'firebase/auth';
import { useUsersStore } from './usersStore';
import { useAgentsStore } from './agentsStore';
import { useRolesStore } from './rolesStore';

interface SpacesState {
    db: Firestore | undefined;
    userRef: DocumentReference<DocumentData, DocumentData> | undefined;
    spacesRef: CollectionReference<DocumentData, DocumentData> | undefined;
    spaces: Record<string, SpaceDefinition>;
    currentSpaceId: string/* | undefined */;
    kanbanStore: ReturnType<typeof useKanbanStore>;
    usersStore: ReturnType<typeof useUsersStore>;
    agentsStore: ReturnType<typeof useAgentsStore>;
    rolesStore: ReturnType<typeof useRolesStore>;
    loading: boolean;
    error: string | null;
}

export const useSpacesStore = defineStore('spaces', {
    state: (): SpacesState => ({
        db: undefined,
        userRef: undefined,
        spacesRef: undefined,
        spaces: {},
        currentSpaceId: 'personal',
        kanbanStore: useKanbanStore(),
        usersStore: useUsersStore(),
        agentsStore: useAgentsStore(),
        rolesStore: useRolesStore(),
        loading: false,
        error: null,
    }),

    actions: {
        async init() {
            this.db = getFirestore();
            const user = inject('user') as Ref<User | null>;
            const userId = user.value?.uid;
            if (!userId) return;
            this.userRef = doc(this.db, "users", userId);
            this.spacesRef = collection(this.db, "users", userId, "spaces");
            await this.loadSpaces();
        },
        async loadHome() {
            if (!this.userRef) return;
            await getDoc(this.userRef)
                .then((documentSnapshot) => {
                    this.spaces["home"] = {
                        name: "Home",
                        server: documentSnapshot.data()?.home
                    };
                })
                .catch((error) => {
                    console.error("Error getting user data: ", error);
                });
        },
        async loadSpaces() {
            if (!this.spacesRef) return;
            this.loading = true;
            this.error = null;
            try {
                await this.loadHome();
                await getDocs(this.spacesRef)
                    .then(async (querySnapshot) => {
                        const ids = querySnapshot.docs.map(doc => doc.id);
                        for (const spaceId of ids) {
                            this.spaces[spaceId] = await getDoc(doc(this.db!, "spaces", spaceId))
                                .then((documentSnapshot) => {
                                    const data = documentSnapshot.data();
                                    if (!data) throw new Error(`Space with ID ${spaceId} not found`);
                                    return data as SpaceDefinition;
                                })
                                .catch((error) => {
                                    throw new Error(`Error getting space data: ${error.message}`);
                                });
                        }
                    })
                    .catch((error) => {
                        console.error("Error getting user spaces: ", error);
                    });
            } catch (error: any) {
                this.error = error.message;
            } finally {
                this.loading = false;
            }
        },
        async setCurrentSpace(spaceId: string) {
            try {
                const space = this.spaces[spaceId];
                if (space) {
                    await connectToKiServer(space.server);
                    localStorage.setItem('lastSpaceId', spaceId);
                    this.currentSpaceId = spaceId;
                    await Promise.all([
                        this.usersStore.fetchUsers(),
                        this.agentsStore.fetchAgents(),
                        this.rolesStore.loadRoles()
                    ]);
                    await this.kanbanStore.loadProjects(space.server);
                } else {
                    throw new Error(`Space with ID ${spaceId} not found`);
                }
            } catch (error: any) {
                this.error = error.message;
            }
        },

        // createNewSpace(spaceId: string) {
        //     try {
        //         // Normalize the space ID (lowercase, replace spaces with hyphens)
        //         const normalizedSpaceId = spaceId.trim().toLowerCase().replace(/\s+/g, '-');

        //         if (!normalizedSpaceId) {
        //             throw new Error('Space ID cannot be empty');
        //         }

        //         createSpace(normalizedSpaceId);
        //         this.spaceIds = getSpaceIds();
        //         this.setCurrentSpace(normalizedSpaceId);
        //     } catch (error: any) {
        //         this.error = error.message;
        //     }
        // },

        // removeSpace(spaceId: string) {
        //     try {
        //         deleteSpace(spaceId);
        //         if (this.currentSpaceId === spaceId) {
        //             this.setCurrentSpace(this.spaceIds[0]);
        //         }
        //         this.spaceIds = getSpaceIds();
        //         this.currentSpaceId = this.spaceIds[0] || 'personal';
        //         this.kanbanStore.loadProjects(this.currentSpaceId);
        //         this.router.push(`/${this.currentSpaceId}`);
        //     } catch (error: any) {
        //         this.error = error.message;
        //     }
        // },

        // renameCurrentSpace(newSpaceId: string) {
        //     try {
        //         // Normalize the space ID (lowercase, replace spaces with hyphens)
        //         const normalizedSpaceId = newSpaceId.trim().toLowerCase().replace(/\s+/g, '-');

        //         if (!normalizedSpaceId) {
        //             throw new Error('Space ID cannot be empty');
        //         }

        //         renameSpace(this.currentSpaceId, normalizedSpaceId);
        //         this.currentSpaceId = normalizedSpaceId;
        //         this.spaceIds = getSpaceIds(); // Update spaceIds directly
        //         this.kanbanStore.loadProjects(this.currentSpaceId); //Reload only necessary data
        //         this.router.push(`/${this.currentSpaceId}`); // Update route
        //     } catch (error: any) {
        //         this.error = error.message;
        //     }
        // }
    }
});
