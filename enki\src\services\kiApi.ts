import { getAuth } from 'firebase/auth';
import type {
    Task, TaskUpdateResponse, User, Agent, Role, NewRole, UpdateRole,
    RoleTreeNode, RoleDetails, SetRoleParentRequest, BulkRelationshipOperation,
    PermissionCheckRequest, PermissionCheckResponse
} from '@/types';

/**
 * Connect to the Ki server using the Firebase ID token
 * This will establish a session with the Ki server
 * @returns Promise<string> User ID if successful
 */
export async function connectToKiServer(server: string): Promise<string> {
    const auth = getAuth();
    const user = auth.currentUser;

    if (!user) {
        throw new Error('User not authenticated');
    }

    // Get the Firebase ID token
    const idToken = await user.getIdToken();

    // Send the token to the Ki server
    const response = await fetch(`${server}/connect`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${idToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to connect to Ki server: ${errorText}`);
    }

    return await response.text();
}

/**
 * Get all tasks from the Ki server
 * @param projectId Project ID
 * @returns Promise<any[]> Array of tasks
 */
export async function getAllTasks(server: string): Promise<Task[]> {
    const response = await fetch(`${server}/tasks`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get tasks: ${errorText}`);
    }

    return await response.json();
}

/**
 * Create a new task on the Ki server
 * @param projectId Project ID
 * @param task Task data
 * @returns Promise<any> Created task
 */
export async function createTask(server: string, task: Omit<Task, 'id'>): Promise<any> {
    const response = await fetch(`${server}/tasks`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(task)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create task: ${errorText}`);
    }

    return await response.json();
}



/**
 * Update a task on the Ki server
 * @param taskId Task ID
 * @param task Task data to update
 * @returns Promise<TaskUpdateResponse> Updated task and affected tasks
 */
export async function updateTask(server: string, task: Task): Promise<TaskUpdateResponse> {
    const response = await fetch(`${server}/tasks/${task.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(task)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update task: ${errorText}`);
    }

    return await response.json();
}

/**
 * Delete a task from the Ki server
 * @param taskId Task ID
 * @returns Promise<void>
 */
export async function deleteTask(server: string, taskId: string): Promise<void> {
    const response = await fetch(`${server}/tasks/${taskId}`, {
        method: 'DELETE',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete task: ${errorText}`);
    }
}

/**
 * Move a task to a different status and/or position
 * @param taskId Task ID
 * @param status New status
 * @param position New position
 * @returns Promise<any> Updated task
 */
export async function moveTask(server: string, taskId: string, status: string, position: number): Promise<any> {
    const response = await fetch(`${server}/tasks/${taskId}/move`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify({
            status,
            position
        })
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to move task: ${errorText}`);
    }

    return await response.json();
}

// User API functions

/**
 * Get all users from the Ki server
 * @param server Server URL
 * @returns Promise<User[]> Array of users
 */
export async function getAllUsers(server: string): Promise<User[]> {
    const response = await fetch(`${server}/users`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get users: ${errorText}`);
    }

    return await response.json();
}

/**
 * Create a new user on the Ki server
 * @param server Server URL
 * @param user User data
 * @returns Promise<User> Created user
 */
export async function createUser(server: string, user: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<User> {
    const response = await fetch(`${server}/users`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(user)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create user: ${errorText}`);
    }

    return await response.json();
}

/**
 * Update a user on the Ki server
 * @param server Server URL
 * @param user User data to update
 * @returns Promise<User> Updated user
 */
export async function updateUser(server: string, user: User): Promise<User> {
    // Extract only the fields that can be updated
    const updateData = {
        email: user.email,
        display_name: user.display_name,
        photo_url: user.photo_url,
        last_login: user.last_login
    };

    const response = await fetch(`${server}/users/${user.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(updateData)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update user: ${errorText}`);
    }

    return await response.json();
}

/**
 * Delete a user from the Ki server
 * @param server Server URL
 * @param userId User ID
 * @returns Promise<void>
 */
export async function deleteUser(server: string, userId: string): Promise<void> {
    const response = await fetch(`${server}/users/${userId}`, {
        method: 'DELETE',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete user: ${errorText}`);
    }
}

// Agent API functions

/**
 * Get all agents from the Ki server
 * @param server Server URL
 * @param activeOnly Whether to get only active agents
 * @returns Promise<Agent[]> Array of agents
 */
export async function getAllAgents(server: string, activeOnly: boolean = false): Promise<Agent[]> {
    const params = new URLSearchParams();
    if (activeOnly) {
        params.append('active_only', 'true');
    }

    const url = `${server}/agents${params.toString() ? '?' + params.toString() : ''}`;
    const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get agents: ${errorText}`);
    }

    return await response.json();
}

/**
 * Create a new agent on the Ki server
 * @param server Server URL
 * @param agent Agent data
 * @returns Promise<Agent> Created agent
 */
export async function createAgent(server: string, agent: Omit<Agent, 'id' | 'created_at' | 'updated_at'>): Promise<Agent> {
    const response = await fetch(`${server}/agents`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(agent)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create agent: ${errorText}`);
    }

    return await response.json();
}

/**
 * Update an agent on the Ki server
 * @param server Server URL
 * @param agent Agent data to update
 * @returns Promise<Agent> Updated agent
 */
export async function updateAgent(server: string, agent: Agent): Promise<Agent> {
    const response = await fetch(`${server}/agents/${agent.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(agent)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update agent: ${errorText}`);
    }

    return await response.json();
}

/**
 * Delete an agent from the Ki server
 * @param server Server URL
 * @param agentId Agent ID
 * @returns Promise<void>
 */
export async function deleteAgent(server: string, agentId: string): Promise<void> {
    const response = await fetch(`${server}/agents/${agentId}`, {
        method: 'DELETE',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete agent: ${errorText}`);
    }
}

// Role API functions

/**
 * Get all roles from the Ki server
 * @param server Server URL
 * @returns Promise<Role[]> Array of roles
 */
export async function getAllRoles(server: string): Promise<Role[]> {
    const response = await fetch(`${server}/roles`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get roles: ${errorText}`);
    }

    return await response.json();
}

/**
 * Get a role by ID from the Ki server
 * @param server Server URL
 * @param roleId Role ID
 * @returns Promise<Role> Role data
 */
export async function getRole(server: string, roleId: string): Promise<Role> {
    const response = await fetch(`${server}/roles/${roleId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get role: ${errorText}`);
    }

    return await response.json();
}

/**
 * Create a new role in the Ki server
 * @param server Server URL
 * @param role New role data
 * @returns Promise<Role> Created role
 */
export async function createRole(server: string, role: NewRole): Promise<Role> {
    const response = await fetch(`${server}/roles`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(role)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create role: ${errorText}`);
    }

    return await response.json();
}

/**
 * Update a role in the Ki server
 * @param server Server URL
 * @param roleId Role ID
 * @param role Updated role data
 * @returns Promise<Role> Updated role
 */
export async function updateRole(server: string, roleId: string, role: UpdateRole): Promise<Role> {
    const response = await fetch(`${server}/roles/${roleId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(role)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update role: ${errorText}`);
    }

    return await response.json();
}

/**
 * Delete a role from the Ki server
 * @param server Server URL
 * @param roleId Role ID
 * @returns Promise<void>
 */
export async function deleteRole(server: string, roleId: string): Promise<void> {
    const response = await fetch(`${server}/roles/${roleId}`, {
        method: 'DELETE',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete role: ${errorText}`);
    }
}

/**
 * Get role hierarchy tree from the Ki server
 * @param server Server URL
 * @returns Promise<RoleTreeNode[]> Role hierarchy tree
 */
export async function getRoleTree(server: string): Promise<RoleTreeNode[]> {
    const response = await fetch(`${server}/roles/tree`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get role tree: ${errorText}`);
    }

    return await response.json();
}

/**
 * Get role details with members from the Ki server
 * @param server Server URL
 * @param roleId Role ID
 * @returns Promise<RoleDetails> Role details with members
 */
export async function getRoleDetails(server: string, roleId: string): Promise<RoleDetails> {
    const response = await fetch(`${server}/roles/${roleId}/details`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors'
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to get role details: ${errorText}`);
    }

    return await response.json();
}

/**
 * Set role parent in the Ki server
 * @param server Server URL
 * @param roleId Role ID
 * @param request Set parent request
 * @returns Promise<void>
 */
export async function setRoleParent(server: string, roleId: string, request: SetRoleParentRequest): Promise<void> {
    const response = await fetch(`${server}/roles/${roleId}/parent`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(request)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to set role parent: ${errorText}`);
    }
}

/**
 * Perform bulk relationship operations in the Ki server
 * @param server Server URL
 * @param operations Bulk operations
 * @returns Promise<void>
 */
export async function bulkRelationshipOperations(server: string, operations: BulkRelationshipOperation[]): Promise<void> {
    const response = await fetch(`${server}/transactions/relationships`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(operations)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to perform bulk operations: ${errorText}`);
    }
}

/**
 * Check permissions in the Ki server
 * @param server Server URL
 * @param request Permission check request
 * @returns Promise<PermissionCheckResponse> Permission check results
 */
export async function checkPermissions(server: string, request: PermissionCheckRequest): Promise<PermissionCheckResponse> {
    const response = await fetch(`${server}/permissions/check`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'include',
        mode: 'cors',
        body: JSON.stringify(request)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to check permissions: ${errorText}`);
    }

    return await response.json();
}
