{"name": "enki-proxy", "type": "module", "version": "1.0.0", "description": "Proxy server for Enki testing", "main": "proxy-server.js", "scripts": {"start": "node proxy-server.js", "dev": "concurrently \"cd ki && bash docker-run.sh\" \"cd enki && npm run dev\" \"cd landing && npm run dev\" \"node proxy-server.js\""}, "dependencies": {"express": "^4.18.2", "http-proxy-middleware": "^2.0.6"}, "devDependencies": {"concurrently": "^8.2.0"}}