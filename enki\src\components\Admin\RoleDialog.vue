<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { useRolesStore } from '@/stores/rolesStore';
import type { Role, NewRole, UpdateRole } from '@/types';

const props = defineProps<{
    modelValue: boolean;
    role?: Role | null;
}>();

const emit = defineEmits<{
    'update:modelValue': [value: boolean];
    'close': [saved: boolean];
}>();

const rolesStore = useRolesStore();

// Form state
const formRef = ref<FormInstance>();
const loading = ref(false);

const formData = ref({
    name: '',
    description: '',
    color: '#6b7280'
});

// Predefined color options
const colorOptions = [
    '#ef4444', // red
    '#f97316', // orange
    '#eab308', // yellow
    '#22c55e', // green
    '#06b6d4', // cyan
    '#3b82f6', // blue
    '#8b5cf6', // violet
    '#ec4899', // pink
    '#6b7280', // gray
    '#1f2937'  // dark gray
];

// Form validation rules
const rules: FormRules = {
    name: [
        { required: true, message: 'Role name is required', trigger: 'blur' },
        { min: 2, max: 50, message: 'Name must be between 2 and 50 characters', trigger: 'blur' },
        {
            validator: (rule, value, callback) => {
                // Check for duplicate names (excluding current role if editing)
                const existingRole = rolesStore.getRoleByName(value);
                if (existingRole && existingRole.id !== props.role?.id) {
                    callback(new Error('Role name already exists'));
                } else {
                    callback();
                }
            },
            trigger: 'blur'
        }
    ],
    description: [
        { max: 200, message: 'Description cannot exceed 200 characters', trigger: 'blur' }
    ]
};

// Computed properties
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

const isEditing = computed(() => !!props.role);
const dialogTitle = computed(() => isEditing.value ? 'Edit Role' : 'Create Role');

// Watch for role changes to populate form
watch(() => props.role, (newRole) => {
    if (newRole) {
        formData.value = {
            name: newRole.name,
            description: newRole.description || '',
            color: newRole.color || '#6b7280'
        };
    } else {
        resetForm();
    }
}, { immediate: true });

// Form methods
const resetForm = () => {
    formData.value = {
        name: '',
        description: '',
        color: '#6b7280'
    };
    nextTick(() => {
        formRef.value?.clearValidate();
    });
};

const handleClose = () => {
    dialogVisible.value = false;
    emit('close', false);
};

const handleSave = async () => {
    if (!formRef.value) return;
    
    try {
        const valid = await formRef.value.validate();
        if (!valid) return;
        
        loading.value = true;
        
        let success = false;
        
        if (isEditing.value && props.role) {
            // Update existing role
            const updateData: UpdateRole = {
                name: formData.value.name !== props.role.name ? formData.value.name : undefined,
                description: formData.value.description !== props.role.description ? formData.value.description : undefined,
                color: formData.value.color !== props.role.color ? formData.value.color : undefined
            };
            
            // Only send fields that have changed
            if (Object.values(updateData).some(value => value !== undefined)) {
                const result = await rolesStore.updateRole(props.role.id, updateData);
                success = !!result;
            } else {
                success = true; // No changes needed
            }
        } else {
            // Create new role
            const newRole: NewRole = {
                name: formData.value.name,
                description: formData.value.description || undefined,
                color: formData.value.color
            };
            
            const result = await rolesStore.createRole(newRole);
            success = !!result;
        }
        
        if (success) {
            ElMessage.success(isEditing.value ? 'Role updated successfully' : 'Role created successfully');
            dialogVisible.value = false;
            emit('close', true);
        }
    } catch (error) {
        console.error('Failed to save role:', error);
        ElMessage.error('Failed to save role');
    } finally {
        loading.value = false;
    }
};

// Color selection
const selectColor = (color: string) => {
    formData.value.color = color;
};
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="500px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="100px"
            label-position="left"
        >
            <el-form-item label="Name" prop="name">
                <el-input
                    v-model="formData.name"
                    placeholder="Enter role name"
                    maxlength="50"
                    show-word-limit
                />
            </el-form-item>
            
            <el-form-item label="Description" prop="description">
                <el-input
                    v-model="formData.description"
                    type="textarea"
                    placeholder="Enter role description (optional)"
                    :rows="3"
                    maxlength="200"
                    show-word-limit
                />
            </el-form-item>
            
            <el-form-item label="Color">
                <div class="color-selection">
                    <div class="color-preview">
                        <div 
                            class="color-circle selected"
                            :style="{ backgroundColor: formData.color }"
                        ></div>
                        <span class="color-value">{{ formData.color }}</span>
                    </div>
                    <div class="color-options">
                        <div
                            v-for="color in colorOptions"
                            :key="color"
                            class="color-circle"
                            :class="{ selected: formData.color === color }"
                            :style="{ backgroundColor: color }"
                            @click="selectColor(color)"
                            :title="color"
                        ></div>
                    </div>
                    <el-input
                        v-model="formData.color"
                        placeholder="#6b7280"
                        class="color-input"
                        maxlength="7"
                    />
                </div>
            </el-form-item>
        </el-form>
        
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose" :disabled="loading">
                    Cancel
                </el-button>
                <el-button 
                    type="primary" 
                    @click="handleSave"
                    :loading="loading"
                >
                    {{ isEditing ? 'Update' : 'Create' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.color-selection {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.color-preview {
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-options {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.color-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.color-circle:hover {
    transform: scale(1.1);
    border-color: var(--el-color-primary);
}

.color-circle.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.color-value {
    font-family: monospace;
    font-size: 12px;
    color: var(--el-text-color-regular);
}

.color-input {
    width: 120px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
